#!/usr/bin/env python3
"""
Test script to verify the COM threading fix for E3.series connection manager

This script tests the updated connection manager to ensure it properly handles
COM apartment threading issues that can occur in GUI applications.

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.e3_connection_manager import E3ConnectionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_com_threading_fix():
    """Test the COM threading fix"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing COM Threading Fix for E3.series Connection Manager")
    logger.info("=" * 65)
    
    try:
        # Create connection manager
        manager = E3ConnectionManager(logger)
        
        logger.info("Attempting to connect to E3.series...")
        logger.info("This should handle COM threading properly now.")
        logger.info("")
        
        # Try to connect
        app = manager.connect_to_e3()
        
        if app:
            logger.info("✓ Successfully connected to E3.series!")
            logger.info("✓ COM threading issue appears to be resolved!")
            
            # Test basic functionality
            try:
                job = app.CreateJobObject()
                if job:
                    logger.info("✓ Successfully created job object")
                    
                    # Try to get device count as a test
                    device_ids_result = job.GetAllDeviceIds()
                    if isinstance(device_ids_result, tuple) and len(device_ids_result) >= 2:
                        device_count = device_ids_result[0]
                        logger.info(f"✓ Found {device_count} devices in the project")
                    
                else:
                    logger.warning("⚠ Could not create job object - no project may be open")
                    
            except Exception as e:
                logger.warning(f"⚠ Could not test project functionality: {e}")
            
            # Clean up
            manager.cleanup()
            logger.info("✓ Connection cleaned up successfully")
            
        else:
            logger.error("✗ Failed to connect to E3.series")
            logger.info("This could be due to:")
            logger.info("  - No E3.series instances running")
            logger.info("  - No project open in E3.series")
            logger.info("  - User cancelled the connection dialog")
            
    except Exception as e:
        if "apartment" in str(e).lower() or "tcl" in str(e).lower():
            logger.error("✗ COM threading issue still exists!")
            logger.error(f"Error: {e}")
            logger.info("")
            logger.info("Possible solutions:")
            logger.info("  1. Restart the application")
            logger.info("  2. Restart E3.series")
            logger.info("  3. Check if other COM applications are interfering")
        else:
            logger.error(f"✗ Unexpected error: {e}")
            import traceback
            logger.debug(traceback.format_exc())
    
    logger.info("")
    logger.info("COM threading test completed!")

def main():
    """Main entry point"""
    try:
        test_com_threading_fix()
    except Exception as e:
        logging.error(f"Unhandled exception during test: {e}")
        import traceback
        logging.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
