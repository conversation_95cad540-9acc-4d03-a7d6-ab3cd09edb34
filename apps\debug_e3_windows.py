#!/usr/bin/env python3
"""
Debug script to show E3.series window titles

This script will show all window titles for each E3.series process to help
understand how to extract project names.

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging
import subprocess

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def get_e3_processes():
    """Get E3.series process IDs"""
    processes = []
    try:
        result = subprocess.run(
            ['tasklist', '/FO', 'CSV'],
            capture_output=True,
            text=True,
            shell=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:
                if line.strip() and 'E3.series.exe' in line:
                    parts = [part.strip('"') for part in line.split('","')]
                    if len(parts) >= 2:
                        try:
                            pid = int(parts[1])
                            processes.append(pid)
                        except ValueError:
                            continue
    except Exception as e:
        logging.error(f"Failed to get processes: {e}")
    
    return processes

def get_windows_for_pid(pid):
    """Get all window titles for a specific process ID"""
    try:
        import win32gui
        import win32process
        
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                if window_pid == pid:
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        windows.append(window_title)
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows
    except Exception as e:
        logging.error(f"Failed to get windows for PID {pid}: {e}")
        return []

def main():
    """Main entry point"""
    logger = logging.getLogger(__name__)
    
    logger.info("E3.series Window Title Debug")
    logger.info("=" * 50)
    
    # Get E3.series processes
    processes = get_e3_processes()
    logger.info(f"Found {len(processes)} E3.series processes")
    
    for pid in processes:
        logger.info(f"\nProcess ID: {pid}")
        logger.info("-" * 30)
        
        windows = get_windows_for_pid(pid)
        if windows:
            logger.info(f"Found {len(windows)} windows:")
            for i, window_title in enumerate(windows, 1):
                logger.info(f"  {i}. '{window_title}'")
                
                # Analyze the title
                if 'E3.series' in window_title:
                    logger.info(f"     -> E3.series window detected")
                    
                    # Try to extract project name
                    if window_title.endswith(" - E3.series"):
                        project = window_title.replace(" - E3.series", "").strip()
                        logger.info(f"     -> Potential project: '{project}'")
                    elif window_title.startswith("E3.series - "):
                        project = window_title.replace("E3.series - ", "").strip()
                        if " - " in project:
                            project = project.split(" - ")[0]
                        logger.info(f"     -> Potential project: '{project}'")
        else:
            logger.info("  No windows found")

if __name__ == "__main__":
    main()
