#!/usr/bin/env python3
"""
Test script to show the E3.series instance selector with project names

This script demonstrates the enhanced E3 connection manager selector dialog
that shows project names for each running E3.series instance.

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.e3_connection_manager import E3ConnectionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_e3_selector():
    """Test the E3.series instance selector dialog"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing E3.series Instance Selector")
    logger.info("=" * 50)
    
    # Create connection manager
    manager = E3ConnectionManager(logger)
    
    logger.info("Attempting to connect to E3.series...")
    logger.info("This will show the selector dialog if multiple instances are found.")
    logger.info("")
    
    # Try to connect - this will show the selector if multiple instances exist
    app = manager.connect_to_e3()
    
    if app:
        logger.info("✓ Successfully connected to E3.series!")
        
        # Get some basic info to verify the connection
        try:
            job = app.CreateJobObject()
            logger.info("✓ Successfully created job object")
            
            # Try to get device count as a test
            device_ids_result = job.GetAllDeviceIds()
            if isinstance(device_ids_result, tuple) and len(device_ids_result) >= 2:
                device_count = device_ids_result[0]
                logger.info(f"✓ Found {device_count} devices in the connected project")
            
        except Exception as e:
            logger.warning(f"Could not get project details: {e}")
            
    else:
        logger.error("✗ Failed to connect to E3.series")
    
    logger.info("")
    logger.info("Selector test completed!")

def main():
    """Main entry point"""
    try:
        test_e3_selector()
    except Exception as e:
        logging.error(f"Unhandled exception during test: {e}")
        import traceback
        logging.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
