#!/usr/bin/env python3
"""
Test script for E3 Connection Manager

This script demonstrates the new E3 connection manager functionality that allows
selecting from multiple running E3.series instances, similar to the VBA ConnectToE3() function.

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.e3_connection_manager import E3ConnectionManager, create_e3_connection

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_e3_connection_manager.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_connection_manager():
    """Test the E3 connection manager functionality"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing E3 Connection Manager")
    logger.info("=" * 50)
    
    # Test 1: Using the connection manager class directly
    logger.info("Test 1: Using E3ConnectionManager class")
    manager = E3ConnectionManager(logger)
    app = manager.connect_to_e3()
    
    if app:
        logger.info("✓ Successfully connected to E3.series using connection manager")
        
        # Get project information
        project_info = manager.get_project_info()
        if project_info:
            project_name, project_path = project_info
            logger.info(f"✓ Project Name: {project_name}")
            logger.info(f"✓ Project Path: {project_path}")
        else:
            logger.warning("⚠ Could not retrieve project information")
        
        # Test creating some E3 objects
        try:
            job = app.CreateJobObject()
            device = job.CreateDeviceObject()
            connection = job.CreateConnectionObject()
            logger.info("✓ Successfully created E3 objects (job, device, connection)")
        except Exception as e:
            logger.error(f"✗ Failed to create E3 objects: {e}")
    else:
        logger.error("✗ Failed to connect to E3.series using connection manager")
    
    logger.info("")
    
    # Test 2: Using the convenience function
    logger.info("Test 2: Using create_e3_connection convenience function")
    app2 = create_e3_connection(logger)
    
    if app2:
        logger.info("✓ Successfully connected to E3.series using convenience function")
        
        # Test getting device count
        try:
            job = app2.CreateJobObject()
            device_ids_result = job.GetAllDeviceIds()
            
            if isinstance(device_ids_result, tuple) and len(device_ids_result) >= 2:
                device_count = device_ids_result[0]
                logger.info(f"✓ Found {device_count} devices in the project")
            else:
                logger.warning("⚠ Unexpected device IDs format")
                
        except Exception as e:
            logger.error(f"✗ Failed to get device count: {e}")
    else:
        logger.error("✗ Failed to connect to E3.series using convenience function")
    
    logger.info("")
    logger.info("E3 Connection Manager test completed")

def main():
    """Main entry point"""
    try:
        test_connection_manager()
    except Exception as e:
        logging.error(f"Unhandled exception during test: {e}")
        import traceback
        logging.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
