#!/usr/bin/env python3
"""
Test script to verify E3.series project name detection

This script tests the enhanced E3 connection manager that shows project names
for each running E3.series instance.

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.e3_connection_manager import E3ConnectionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_project_detection():
    """Test E3.series project name detection"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing E3.series Project Name Detection")
    logger.info("=" * 50)
    
    # Create connection manager
    manager = E3ConnectionManager(logger)
    
    # Test the detection methods directly
    logger.info("Detecting E3.series instances with project information...")
    instances = manager._get_e3_instances_basic()
    
    logger.info(f"Found {len(instances)} E3.series instances:")
    logger.info("")
    
    for i, instance in enumerate(instances, 1):
        logger.info(f"  Instance {i}:")
        logger.info(f"    Process ID: {instance.process_id}")
        logger.info(f"    Project: {instance.project_name}")
        logger.info(f"    Display: {instance}")
        logger.info("")
    
    if len(instances) > 1:
        logger.info("✓ Multiple instances detected with project information!")
        logger.info("  The connection manager will show these details in the selection dialog.")
    elif len(instances) == 1:
        logger.info("✓ Single instance detected with project information")
    else:
        logger.info("✗ No E3.series instances detected")
    
    logger.info("")
    logger.info("Project detection test completed!")

def main():
    """Main entry point"""
    try:
        test_project_detection()
    except Exception as e:
        logging.error(f"Unhandled exception during test: {e}")
        import traceback
        logging.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
