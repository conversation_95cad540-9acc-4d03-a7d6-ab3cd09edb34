#!/usr/bin/env python3
"""
E3 Connection Manager

This module provides functionality to connect to E3.series applications with support
for selecting from multiple running instances, similar to the VBA ConnectToE3() function.

Based on the VBA implementation that uses CT.Dispatcher and CT.DispatcherViewer to
handle multiple E3 instances.

Author: <PERSON>
Date: 2025-07-15
"""

import logging
import sys
import pythoncom
import win32com.client
import e3series
from typing import Optional, List, Tuple
import tkinter as tk
from tkinter import messagebox, ttk


class E3InstanceInfo:
    """Information about an E3.series instance"""
    
    def __init__(self, process_id: int, window_title: str, project_name: str = ""):
        self.process_id = process_id
        self.window_title = window_title
        self.project_name = project_name
        
    def __str__(self):
        if self.project_name:
            return f"PID {self.process_id}: {self.project_name}"
        else:
            return f"PID {self.process_id}: {self.window_title}"


class E3InstanceSelector:
    """GUI for selecting an E3.series instance when multiple are running"""
    
    def __init__(self, instances: List[E3InstanceInfo]):
        self.instances = instances
        self.selected_instance = None
        self.root = None
        
    def show_selector(self) -> Optional[E3InstanceInfo]:
        """Show the instance selector dialog and return the selected instance"""
        self.root = tk.Tk()
        self.root.title("Select E3.series Instance")
        self.root.geometry("500x300")
        self.root.resizable(False, False)
        
        # Make window modal and center it
        self.root.transient()
        self.root.grab_set()
        self._center_window()
        
        # Create widgets
        self._create_widgets()
        
        # Run the dialog
        self.root.mainloop()
        
        return self.selected_instance
    
    def _center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """Create the dialog widgets"""
        # Title
        title_label = tk.Label(
            self.root,
            text="Multiple E3.series instances detected",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Instructions
        instruction_label = tk.Label(
            self.root,
            text="Please select which E3.series instance to connect to:",
            font=("Arial", 10)
        )
        instruction_label.pack(pady=(0, 20))
        
        # Listbox frame
        list_frame = tk.Frame(self.root)
        list_frame.pack(padx=20, pady=10, fill="both", expand=True)
        
        # Listbox with scrollbar
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side="right", fill="y")
        
        self.listbox = tk.Listbox(
            list_frame,
            yscrollcommand=scrollbar.set,
            font=("Arial", 10),
            height=8
        )
        self.listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=self.listbox.yview)
        
        # Populate listbox
        for instance in self.instances:
            self.listbox.insert(tk.END, str(instance))
        
        # Select first item by default
        if self.instances:
            self.listbox.selection_set(0)
        
        # Button frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # OK button
        ok_button = tk.Button(
            button_frame,
            text="Connect",
            command=self._on_ok,
            width=12,
            font=("Arial", 10, "bold")
        )
        ok_button.pack(side="left", padx=10)
        
        # Cancel button
        cancel_button = tk.Button(
            button_frame,
            text="Cancel",
            command=self._on_cancel,
            width=12
        )
        cancel_button.pack(side="left", padx=10)
        
        # Bind double-click to OK
        self.listbox.bind("<Double-Button-1>", lambda e: self._on_ok())
        
        # Bind Enter key to OK
        self.root.bind("<Return>", lambda e: self._on_ok())
        self.root.bind("<Escape>", lambda e: self._on_cancel())
    
    def _on_ok(self):
        """Handle OK button click"""
        selection = self.listbox.curselection()
        if selection:
            self.selected_instance = self.instances[selection[0]]
        self.root.destroy()
    
    def _on_cancel(self):
        """Handle Cancel button click"""
        self.selected_instance = None
        self.root.destroy()


class E3ConnectionManager:
    """Manager for connecting to E3.series applications"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.app = None
        
    def connect_to_e3(self) -> Optional[object]:
        """
        Connect to E3.series application with support for multiple instances.
        
        Returns:
            E3 application object if successful, None otherwise
        """
        try:
            # Initialize COM
            pythoncom.CoInitialize()
            
            # Try to get E3 instances using CT.Dispatcher
            instances = self._get_e3_instances_via_dispatcher()
            
            if len(instances) > 1:
                # Multiple instances - show selector
                self.logger.info(f"Found {len(instances)} E3.series instances")
                selector = E3InstanceSelector(instances)
                selected = selector.show_selector()
                
                if selected is None:
                    self.logger.info("User cancelled E3 instance selection")
                    return None
                
                self.logger.info(f"User selected: {selected}")
                # For now, connect to the first available (CT.Dispatcher doesn't provide PID-specific connection)
                self.app = e3series.Application()
                
            elif len(instances) == 1:
                # Single instance - connect directly
                self.logger.info("Found single E3.series instance, connecting...")
                self.app = e3series.Application()
                
            else:
                # No instances found via dispatcher, try fallback method
                self.logger.info("No instances found via CT.Dispatcher, trying fallback...")
                instances = self._get_e3_instances_via_wmi()
                
                if len(instances) > 1:
                    messagebox.showwarning(
                        "Multiple E3 Instances",
                        f"Found {len(instances)} E3.series processes running.\n"
                        "Please close all but one E3.series instance and try again."
                    )
                    return None
                elif len(instances) == 1:
                    self.app = e3series.Application()
                else:
                    self.logger.error("No E3.series instances found")
                    messagebox.showerror(
                        "E3.series Not Found",
                        "No running E3.series instances found.\n"
                        "Please start E3.series with a project open and try again."
                    )
                    return None
            
            # Verify connection by creating a job object
            if self.app:
                job = self.app.CreateJobObject()
                if job:
                    self.logger.info("Successfully connected to E3.series application")
                    return self.app
                else:
                    self.logger.error("Failed to create job object - no project may be open")
                    messagebox.showerror(
                        "E3.series Connection Error",
                        "Connected to E3.series but no project appears to be open.\n"
                        "Please open a project in E3.series and try again."
                    )
                    return None
            
        except Exception as e:
            self.logger.error(f"Failed to connect to E3.series: {e}")
            messagebox.showerror(
                "E3.series Connection Error",
                f"Failed to connect to E3.series:\n{str(e)}\n\n"
                "Please ensure E3.series is running with a project open."
            )
            return None
        
        return None

    def _get_e3_instances_via_dispatcher(self) -> List[E3InstanceInfo]:
        """
        Get E3.series instances using CT.Dispatcher (preferred method).

        Returns:
            List of E3InstanceInfo objects
        """
        instances = []

        try:
            # Try to create CT.Dispatcher object
            disp = win32com.client.CreateObject("CT.Dispatcher")

            # Get E3 applications
            result = disp.GetE3Applications()

            if isinstance(result, tuple) and len(result) >= 2:
                count = result[0]
                app_list = result[1]

                self.logger.info(f"CT.Dispatcher found {count} E3.series applications")

                if count > 0:
                    # If we have multiple applications, we can enumerate them
                    if isinstance(app_list, (list, tuple)):
                        for i, app_info in enumerate(app_list):
                            # Extract information about each instance
                            # Note: The exact structure depends on E3.series version
                            instances.append(E3InstanceInfo(
                                process_id=i + 1,  # Use index as pseudo-PID
                                window_title=f"E3.series Instance {i + 1}",
                                project_name=""  # Would need additional API calls to get project name
                            ))
                    else:
                        # Single instance
                        instances.append(E3InstanceInfo(
                            process_id=1,
                            window_title="E3.series Instance",
                            project_name=""
                        ))

        except Exception as e:
            self.logger.debug(f"CT.Dispatcher not available or failed: {e}")

        return instances

    def _get_e3_instances_via_wmi(self) -> List[E3InstanceInfo]:
        """
        Get E3.series instances using WMI (fallback method).

        Returns:
            List of E3InstanceInfo objects
        """
        instances = []

        try:
            import wmi

            # Connect to WMI
            c = wmi.WMI()

            # Query for E3.series processes
            processes = c.query("SELECT * FROM Win32_Process WHERE Name LIKE '%E3%' OR Caption LIKE '%E3.series%'")

            for process in processes:
                # Filter for actual E3.series processes
                if "E3.series" in str(process.Caption) or "E3" in str(process.Name):
                    instances.append(E3InstanceInfo(
                        process_id=process.ProcessId,
                        window_title=str(process.Caption),
                        project_name=""  # Would need additional methods to get project name
                    ))

        except ImportError:
            self.logger.debug("WMI module not available, using basic process enumeration")
            # Fallback to basic process enumeration
            instances = self._get_e3_instances_basic()
        except Exception as e:
            self.logger.debug(f"WMI query failed: {e}")
            instances = self._get_e3_instances_basic()

        return instances

    def _get_e3_instances_basic(self) -> List[E3InstanceInfo]:
        """
        Basic E3.series instance detection using tasklist.

        Returns:
            List of E3InstanceInfo objects
        """
        instances = []

        try:
            import subprocess

            # Use tasklist to find E3.series processes
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq E3*.exe', '/FO', 'CSV'],
                capture_output=True,
                text=True,
                shell=True
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                # Skip header line
                for line in lines[1:]:
                    if line.strip() and 'E3' in line:
                        # Parse CSV format: "Image Name","PID","Session Name","Session#","Mem Usage"
                        parts = [part.strip('"') for part in line.split('","')]
                        if len(parts) >= 2:
                            try:
                                pid = int(parts[1])
                                instances.append(E3InstanceInfo(
                                    process_id=pid,
                                    window_title=parts[0],
                                    project_name=""
                                ))
                            except ValueError:
                                continue

        except Exception as e:
            self.logger.debug(f"Basic process enumeration failed: {e}")

        return instances

    def get_project_info(self) -> Optional[Tuple[str, str]]:
        """
        Get information about the currently open project.

        Returns:
            Tuple of (project_name, project_path) if successful, None otherwise
        """
        if not self.app:
            return None

        try:
            job = self.app.CreateJobObject()
            if job:
                project_name = job.GetProjectName()
                project_path = job.GetProjectPath()
                return (project_name, project_path)
        except Exception as e:
            self.logger.debug(f"Failed to get project info: {e}")

        return None


def create_e3_connection(logger: Optional[logging.Logger] = None) -> Optional[object]:
    """
    Convenience function to create an E3.series connection.

    Args:
        logger: Optional logger instance

    Returns:
        E3 application object if successful, None otherwise
    """
    manager = E3ConnectionManager(logger)
    return manager.connect_to_e3()
