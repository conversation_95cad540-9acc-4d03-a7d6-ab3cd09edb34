#!/usr/bin/env python3
"""
Test script to verify worker thread detection in E3 connection manager

Author: <PERSON>
Date: 2025-07-15
"""

import os
import sys
import logging
import threading
import time

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from lib.e3_connection_manager import E3ConnectionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_in_worker_thread():
    """Test connection in worker thread"""
    logger = logging.getLogger("worker_thread")
    
    logger.info("=== WORKER THREAD TEST ===")
    logger.info(f"Current thread: {threading.current_thread().name}")
    logger.info(f"Is main thread: {threading.current_thread() is threading.main_thread()}")
    
    try:
        manager = E3ConnectionManager(logger)
        app = manager.connect_to_e3()
        
        if app:
            logger.info("✓ Successfully connected in worker thread!")
        else:
            logger.error("✗ Failed to connect in worker thread")
            
    except Exception as e:
        logger.error(f"✗ Error in worker thread: {e}")

def test_in_main_thread():
    """Test connection in main thread"""
    logger = logging.getLogger("main_thread")
    
    logger.info("=== MAIN THREAD TEST ===")
    logger.info(f"Current thread: {threading.current_thread().name}")
    logger.info(f"Is main thread: {threading.current_thread() is threading.main_thread()}")
    
    try:
        manager = E3ConnectionManager(logger)
        app = manager.connect_to_e3()
        
        if app:
            logger.info("✓ Successfully connected in main thread!")
        else:
            logger.error("✗ Failed to connect in main thread")
            
    except Exception as e:
        logger.error(f"✗ Error in main thread: {e}")

def main():
    """Main entry point"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing E3 Connection Manager Thread Detection")
    logger.info("=" * 60)
    
    # Test in main thread first
    test_in_main_thread()
    
    time.sleep(2)
    
    # Test in worker thread
    logger.info("")
    logger.info("Starting worker thread test...")
    
    worker_thread = threading.Thread(
        target=test_in_worker_thread,
        name="E3TestWorker",
        daemon=True
    )
    
    worker_thread.start()
    worker_thread.join(timeout=30)  # Wait up to 30 seconds
    
    if worker_thread.is_alive():
        logger.error("Worker thread test timed out!")
    else:
        logger.info("Worker thread test completed")
    
    logger.info("")
    logger.info("Thread detection test completed!")

if __name__ == "__main__":
    main()
